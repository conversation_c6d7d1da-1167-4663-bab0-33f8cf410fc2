# 🎮 Rock Paper Scissors Online

A real-time multiplayer Rock Paper Scissors game with room-based gameplay, built with Python Flask backend and vanilla JavaScript frontend.

## ✨ Features

- **Room-based Multiplayer**: Create or join games using 6-digit room codes
- **Real-time Gameplay**: WebSocket communication for instant updates
- **Rich UI/UX**: Vibrant, responsive design that works on desktop and mobile
- **Game Management**: Automatic room cleanup and player disconnection handling
- **Security**: Basic anti-cheating measures and input validation

## 🏗️ Architecture

### Frontend (Static Files)
- **HTML/CSS/JavaScript**: No frameworks required for easy hosting
- **WebSocket Client**: Real-time communication with server
- **Responsive Design**: Works on all device sizes

### Backend (Python)
- **Flask + SocketIO**: WebSocket server for real-time communication
- **Game Manager**: Room creation, player matching, and game logic
- **Automatic Cleanup**: Removes inactive rooms and handles disconnections

## 🚀 Quick Start

### Prerequisites
- Python 3.7 or higher
- pip (Python package manager)

### Installation

1. **Clone or download the project files**
   ```bash
   cd rock-paper-scissors-online
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Start the server**
   ```bash
   python server.py
   ```

4. **Open your browser**
   - Navigate to `http://localhost:5000`
   - The game is now ready to play!

## 🎯 How to Play

### Creating a Game
1. Enter your name
2. Click "Create New Game"
3. Share the 6-digit room code with a friend
4. Wait for them to join

### Joining a Game
1. Enter your name
2. Click "Join Game"
3. Enter the 6-digit room code
4. Start playing!

### Gameplay
1. Both players select Rock, Paper, or Scissors
2. Choices are revealed simultaneously
3. Winner is determined by classic rules:
   - Rock beats Scissors
   - Paper beats Rock
   - Scissors beats Paper
4. Play again or leave the room

## 🌐 Deployment Options

### Option 1: Local Development
- Run `python server.py`
- Access at `http://localhost:5000`

### Option 2: Cloud Hosting (Render.com + Netlify)

#### Backend Deployment (Render.com)
1. **Create a Render.com account** and connect your GitHub repository

2. **Deploy the backend**:
   - Create a new Web Service on Render.com
   - Connect your repository
   - Set the following:
     - **Build Command**: `pip install -r requirements.txt`
     - **Start Command**: `python server.py`
     - **Environment**: Python 3
   - Add environment variables:
     - `DEBUG`: `false`
     - `SECRET_KEY`: (generate a random secret key)

3. **Note your Render URL**: `https://your-app-name.onrender.com`

#### Frontend Deployment (Netlify)
1. **Update the server URL** in `frontend/script.js`:
   ```javascript
   // Replace 'your-app-name' with your actual Render app name
   production: 'https://your-app-name.onrender.com'
   ```

2. **Deploy to Netlify**:
   - Drag and drop the `frontend` folder to Netlify
   - Or connect your GitHub repository and set:
     - **Build command**: (leave empty)
     - **Publish directory**: `frontend`

3. **Your game is now live!**
   - Frontend: `https://your-netlify-site.netlify.app`
   - Backend: `https://your-app-name.onrender.com`

#### Quick Deploy Steps:
1. Fork/clone this repository
2. Deploy backend to Render.com (takes ~5 minutes)
3. Update the server URL in `frontend/script.js`
4. Deploy frontend to Netlify (takes ~1 minute)
5. Share your Netlify URL with friends!

### Option 3: VPS/Dedicated Server
1. Install Python and dependencies
2. Use a process manager like PM2 or systemd
3. Set up a reverse proxy with Nginx
4. Configure SSL certificates

## 🔧 Configuration

### Environment Variables
- `PORT`: Server port (default: 5000)
- `SECRET_KEY`: Flask secret key for sessions

### Game Settings
Edit `game_manager.py` to modify:
- Room code length (default: 6 characters)
- Room expiration time (default: 30 minutes)
- Maximum players per room (default: 2)

### Security Settings
- Change the Flask secret key in production
- Configure CORS origins for your domain
- Add rate limiting if needed

## 📁 Project Structure

```
rock-paper-scissors-online/
├── server.py              # Flask server with WebSocket support
├── game_manager.py        # Game logic and room management
├── index.html            # Main game interface
├── style.css             # Responsive styling
├── script.js             # Frontend game logic
├── requirements.txt      # Python dependencies
└── README.md            # This file
```

## 🛠️ Development

### Adding Features
- **Spectator Mode**: Allow observers in rooms
- **Tournament Mode**: Multiple rounds with scoring
- **Chat System**: In-game messaging
- **Statistics**: Win/loss tracking
- **Custom Rules**: Different game variations

### Testing
- Test with multiple browser tabs for local development
- Use browser developer tools to simulate network issues
- Test on different devices and screen sizes

## 🐛 Troubleshooting

### Common Issues

**Connection Problems**
- Check if the server is running
- Verify firewall settings
- Ensure WebSocket support in browser

**Room Not Found**
- Check room code spelling
- Room may have expired (30 minutes)
- Server may have restarted

**Game Not Starting**
- Ensure both players are connected
- Refresh the page and rejoin
- Check browser console for errors

### Debug Mode
Enable debug mode in `server.py`:
```python
socketio.run(app, debug=True)
```

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For issues and questions:
- Check the troubleshooting section
- Review browser console for errors
- Ensure all dependencies are installed correctly

---

**Enjoy playing Rock Paper Scissors Online! 🎉**
