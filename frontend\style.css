/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap');

body {
    font-family: 'Exo 2', 'Segoe UI', sans-serif;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
        linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 100%);
    min-height: 100vh;
    color: #e0e6ed;
    overflow-x: hidden;
    position: relative;
}

/* Animated background particles */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.4), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.2), transparent);
    background-repeat: repeat;
    background-size: 200px 200px;
    animation: sparkle 20s linear infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes sparkle {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-200px); }
}

.container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 2;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
}

header h1 {
    font-family: 'Orbitron', monospace;
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #00f5ff, #ff00ff, #ffff00, #00ff00);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(0, 245, 255, 0.5);
    position: relative;
}

header h1::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shine 2s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

header p {
    font-size: 1.2rem;
    color: #64ffda;
    font-weight: 300;
    text-shadow: 0 0 10px rgba(100, 255, 218, 0.5);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 0 0 10px rgba(100, 255, 218, 0.5); }
    to { text-shadow: 0 0 20px rgba(100, 255, 218, 0.8), 0 0 30px rgba(100, 255, 218, 0.3); }
}

/* Screen Management */
.screen {
    display: none;
    flex: 1;
    background:
        linear-gradient(135deg, rgba(15, 15, 35, 0.95) 0%, rgba(26, 26, 46, 0.95) 100%);
    border: 2px solid;
    border-image: linear-gradient(45deg, #00f5ff, #ff00ff, #ffff00, #00ff00) 1;
    border-radius: 25px;
    padding: 2.5rem;
    box-shadow:
        0 0 50px rgba(0, 245, 255, 0.3),
        inset 0 0 50px rgba(255, 255, 255, 0.05),
        0 20px 40px rgba(0, 0, 0, 0.4);
    animation: screenEnter 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.screen::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: scanline 3s ease-in-out infinite;
}

.screen.active {
    display: flex;
    flex-direction: column;
}

@keyframes screenEnter {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(50px);
        filter: blur(10px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
        filter: blur(0);
    }
}

@keyframes scanline {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Welcome Screen */
.welcome-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    gap: 2rem;
}

/* Input Groups */
.input-group {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    width: 100%;
    max-width: 350px;
    position: relative;
}

.input-group label {
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    color: #64ffda;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 0 10px rgba(100, 255, 218, 0.5);
}

input[type="text"] {
    padding: 15px 20px;
    border: 2px solid #00f5ff;
    border-radius: 15px;
    font-size: 1.1rem;
    font-family: 'Exo 2', sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
    background: rgba(15, 15, 35, 0.8);
    color: #e0e6ed;
    box-shadow:
        0 0 20px rgba(0, 245, 255, 0.3),
        inset 0 0 20px rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

input[type="text"]:focus {
    outline: none;
    border-color: #ff00ff;
    background: rgba(15, 15, 35, 0.9);
    box-shadow:
        0 0 30px rgba(255, 0, 255, 0.5),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
    transform: scale(1.02);
}

input[type="text"]::placeholder {
    color: rgba(224, 230, 237, 0.5);
}

/* Buttons */
.button-group {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
    width: 100%;
    max-width: 350px;
}

.btn {
    padding: 18px 30px;
    border: 2px solid;
    border-radius: 15px;
    font-family: 'Orbitron', monospace;
    font-size: 1.1rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.btn:active {
    transform: translateY(0) scale(0.98);
}

.btn-primary {
    background: linear-gradient(135deg, #00f5ff 0%, #0080ff 50%, #8000ff 100%);
    border-color: #00f5ff;
    color: #ffffff;
    box-shadow:
        0 0 30px rgba(0, 245, 255, 0.4),
        0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #00f5ff 0%, #ff00ff 50%, #ffff00 100%);
    border-color: #ff00ff;
    box-shadow:
        0 0 40px rgba(255, 0, 255, 0.6),
        0 15px 35px rgba(0, 0, 0, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #ff00ff 0%, #ff0080 50%, #ff8000 100%);
    border-color: #ff00ff;
    color: #ffffff;
    box-shadow:
        0 0 30px rgba(255, 0, 255, 0.4),
        0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #ff00ff 0%, #00ff00 50%, #00ffff 100%);
    border-color: #00ff00;
    box-shadow:
        0 0 40px rgba(0, 255, 0, 0.6),
        0 15px 35px rgba(0, 0, 0, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #ff0040 0%, #ff4000 50%, #ff8000 100%);
    border-color: #ff0040;
    color: #ffffff;
    box-shadow:
        0 0 30px rgba(255, 0, 64, 0.4),
        0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ff0040 0%, #ff0000 50%, #ff4000 100%);
    border-color: #ff0000;
    box-shadow:
        0 0 40px rgba(255, 0, 0, 0.6),
        0 15px 35px rgba(0, 0, 0, 0.4);
}

.btn-small {
    padding: 10px 20px;
    font-size: 0.9rem;
    background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);
    border-color: #00ffff;
    color: #ffffff;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Room Code Display */
.room-info {
    text-align: center;
    margin-bottom: 2rem;
}

.room-code-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    padding: 2rem;
    background: linear-gradient(135deg, rgba(0, 245, 255, 0.2) 0%, rgba(255, 0, 255, 0.2) 100%);
    border: 2px solid #00f5ff;
    border-radius: 20px;
    color: #e0e6ed;
    box-shadow:
        0 0 40px rgba(0, 245, 255, 0.4),
        inset 0 0 40px rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
}

.room-code-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: roomCodeScan 4s ease-in-out infinite;
}

@keyframes roomCodeScan {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

.room-code-display label {
    font-family: 'Orbitron', monospace;
    font-size: 1.2rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: #64ffda;
    text-shadow: 0 0 15px rgba(100, 255, 218, 0.8);
}

.room-code {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 900;
    letter-spacing: 0.3em;
    padding: 1rem 1.5rem;
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid #ffff00;
    border-radius: 15px;
    color: #ffff00;
    text-shadow: 0 0 20px rgba(255, 255, 0, 0.8);
    box-shadow:
        0 0 30px rgba(255, 255, 0, 0.4),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
    animation: codeGlow 2s ease-in-out infinite alternate;
    position: relative;
    z-index: 1;
}

@keyframes codeGlow {
    from {
        text-shadow: 0 0 20px rgba(255, 255, 0, 0.8);
        box-shadow:
            0 0 30px rgba(255, 255, 0, 0.4),
            inset 0 0 20px rgba(255, 255, 255, 0.1);
    }
    to {
        text-shadow: 0 0 30px rgba(255, 255, 0, 1);
        box-shadow:
            0 0 50px rgba(255, 255, 0, 0.6),
            inset 0 0 20px rgba(255, 255, 255, 0.15);
    }
}

/* Players List */
.players-list h3 {
    text-align: center;
    margin-bottom: 1rem;
    color: #555;
}

.players-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.player-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 10px;
    font-weight: 500;
}

.player-status {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Game Screen */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.room-code-small {
    font-weight: 600;
    color: #667eea;
}

.game-status {
    text-align: center;
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(0, 245, 255, 0.2) 0%, rgba(255, 0, 255, 0.2) 100%);
    border: 2px solid #00f5ff;
    border-radius: 15px;
    color: #64ffda;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 0 20px rgba(100, 255, 218, 0.8);
    box-shadow:
        0 0 30px rgba(0, 245, 255, 0.3),
        inset 0 0 30px rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    animation: statusGlow 2s ease-in-out infinite alternate;
}

@keyframes statusGlow {
    from {
        text-shadow: 0 0 20px rgba(100, 255, 218, 0.8);
        box-shadow:
            0 0 30px rgba(0, 245, 255, 0.3),
            inset 0 0 30px rgba(255, 255, 255, 0.05);
    }
    to {
        text-shadow: 0 0 30px rgba(100, 255, 218, 1);
        box-shadow:
            0 0 50px rgba(0, 245, 255, 0.5),
            inset 0 0 30px rgba(255, 255, 255, 0.1);
    }
}

/* Choice Buttons */
.choices-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.choice-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2.5rem 1.5rem;
    border: 3px solid #00f5ff;
    border-radius: 20px;
    background:
        linear-gradient(135deg, rgba(0, 245, 255, 0.1) 0%, rgba(255, 0, 255, 0.1) 100%);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    font-family: 'Orbitron', monospace;
    font-size: 1.1rem;
    font-weight: 700;
    color: #e0e6ed;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow:
        0 0 30px rgba(0, 245, 255, 0.3),
        inset 0 0 30px rgba(255, 255, 255, 0.05);
}

.choice-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.choice-btn:hover::before {
    transform: translateX(100%);
}

.choice-btn:hover {
    transform: translateY(-8px) scale(1.05);
    border-color: #ff00ff;
    box-shadow:
        0 0 50px rgba(255, 0, 255, 0.5),
        0 20px 40px rgba(0, 0, 0, 0.4),
        inset 0 0 30px rgba(255, 255, 255, 0.1);
}

.choice-btn.selected {
    border-color: #ffff00;
    background: linear-gradient(135deg, rgba(255, 255, 0, 0.2) 0%, rgba(255, 0, 255, 0.2) 100%);
    transform: translateY(-5px) scale(1.02);
    box-shadow:
        0 0 60px rgba(255, 255, 0, 0.6),
        0 15px 35px rgba(0, 0, 0, 0.4);
    animation: selectedPulse 1.5s ease-in-out infinite;
}

@keyframes selectedPulse {
    0%, 100% {
        box-shadow:
            0 0 60px rgba(255, 255, 0, 0.6),
            0 15px 35px rgba(0, 0, 0, 0.4);
    }
    50% {
        box-shadow:
            0 0 80px rgba(255, 255, 0, 0.8),
            0 15px 35px rgba(0, 0, 0, 0.4);
    }
}

.choice-emoji {
    font-size: 4rem;
    filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.5));
    transition: all 0.3s ease;
}

.choice-btn:hover .choice-emoji {
    font-size: 4.5rem;
    filter: drop-shadow(0 0 30px rgba(255, 0, 255, 0.8));
    animation: emojiFloat 0.6s ease-in-out;
}

.choice-btn.selected .choice-emoji {
    font-size: 4.2rem;
    filter: drop-shadow(0 0 25px rgba(255, 255, 0, 0.9));
    animation: emojiPulse 1.5s ease-in-out infinite;
}

@keyframes emojiFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@keyframes emojiPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.choice-name {
    font-size: 1.2rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Results Screen */
.results-content {
    text-align: center;
}

.result-header h2 {
    margin-bottom: 2rem;
    font-size: 2rem;
    color: #333;
}

.choices-display {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 2rem 0;
    flex-wrap: wrap;
    gap: 1rem;
}

.player-choice {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.player-name {
    font-weight: 600;
    color: #555;
}

.choice-display {
    font-size: 4rem;
    padding: 1rem;
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 50%;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.vs-divider {
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
}

.result-message {
    font-size: 1.3rem;
    font-weight: 600;
    margin: 2rem 0;
    padding: 1rem;
    border-radius: 10px;
}

.result-message.win {
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    color: #2d5a27;
}

.result-message.lose {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    color: #d63031;
}

.result-message.tie {
    background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
    color: #2d3436;
}

/* Connection Status */
.connection-status {
    position: fixed;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255,255,255,0.9);
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1001;
}

.toast {
    background: #333;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    animation: toastSlide 0.3s ease-out;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}

@keyframes toastSlide {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Error Messages */
.error-message {
    color: #e74c3c;
    font-weight: 500;
    text-align: center;
    margin-top: 1rem;
    padding: 0.5rem;
    background: rgba(231, 76, 60, 0.1);
    border-radius: 5px;
    display: none;
}

.error-message.show {
    display: block;
}

/* Status Messages */
.status-message {
    text-align: center;
    font-size: 1.1rem;
    color: #666;
    margin: 2rem 0;
    padding: 1rem;
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    border-radius: 10px;
}

.choice-feedback {
    text-align: center;
    font-size: 1.1rem;
    font-weight: 600;
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 10px;
    display: none;
}

.choice-feedback.show {
    display: block;
}

.choice-feedback.waiting {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    color: #d63031;
}

.choice-feedback.recorded {
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    color: #2d5a27;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .screen {
        padding: 1.5rem;
    }
    
    .choices-container {
        grid-template-columns: 1fr;
    }
    
    .choice-btn {
        padding: 1.5rem 1rem;
    }
    
    .choice-emoji {
        font-size: 2.5rem;
    }
    
    .choices-display {
        flex-direction: column;
        gap: 2rem;
    }
    
    .vs-divider {
        order: 2;
    }
    
    .game-header {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .room-code {
        font-size: 1.5rem;
    }

    .choice-display {
        width: 80px;
        height: 80px;
        font-size: 3rem;
    }

    .button-group {
        gap: 0.5rem;
    }
}

/* Additional Utility Classes */
.hidden {
    display: none !important;
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}
