import random
import string
import time
from typing import Dict, Optional, List
from dataclasses import dataclass, field
from enum import Enum

class GameState(Enum):
    WAITING = "waiting"
    PLAYING = "playing"
    FINISHED = "finished"

class Choice(Enum):
    ROCK = "rock"
    PAPER = "paper"
    SCISSORS = "scissors"

@dataclass
class Player:
    id: str
    name: str
    choice: Optional[Choice] = None
    connected: bool = True
    last_activity: float = field(default_factory=time.time)

@dataclass
class GameRoom:
    code: str
    players: List[Player] = field(default_factory=list)
    state: GameState = GameState.WAITING
    winner: Optional[str] = None
    created_at: float = field(default_factory=time.time)
    last_activity: float = field(default_factory=time.time)
    
    def add_player(self, player: Player) -> bool:
        if len(self.players) >= 2:
            return False
        self.players.append(player)
        self.last_activity = time.time()
        return True
    
    def remove_player(self, player_id: str) -> bool:
        for i, player in enumerate(self.players):
            if player.id == player_id:
                self.players.pop(i)
                self.last_activity = time.time()
                return True
        return False
    
    def get_player(self, player_id: str) -> Optional[Player]:
        for player in self.players:
            if player.id == player_id:
                return player
        return None
    
    def is_full(self) -> bool:
        return len(self.players) >= 2
    
    def both_players_chose(self) -> bool:
        return len(self.players) == 2 and all(p.choice is not None for p in self.players)
    
    def reset_choices(self):
        for player in self.players:
            player.choice = None
        self.winner = None
        self.state = GameState.PLAYING
        self.last_activity = time.time()

class GameManager:
    def __init__(self):
        self.rooms: Dict[str, GameRoom] = {}
        self.player_to_room: Dict[str, str] = {}
    
    def generate_room_code(self) -> str:
        """Generate a unique 6-digit alphanumeric room code"""
        while True:
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
            if code not in self.rooms:
                return code
    
    def create_room(self, player_id: str, player_name: str) -> str:
        """Create a new game room and add the creator as first player"""
        code = self.generate_room_code()
        room = GameRoom(code=code)
        player = Player(id=player_id, name=player_name)
        room.add_player(player)
        
        self.rooms[code] = room
        self.player_to_room[player_id] = code
        return code
    
    def join_room(self, code: str, player_id: str, player_name: str) -> tuple[bool, str]:
        """Join an existing room"""
        if code not in self.rooms:
            return False, "Room not found"
        
        room = self.rooms[code]
        if room.is_full():
            return False, "Room is full"
        
        player = Player(id=player_id, name=player_name)
        if room.add_player(player):
            self.player_to_room[player_id] = code
            if room.is_full():
                room.state = GameState.PLAYING
            return True, "Joined successfully"
        
        return False, "Failed to join room"
    
    def leave_room(self, player_id: str) -> Optional[str]:
        """Remove player from their current room"""
        if player_id not in self.player_to_room:
            return None
        
        code = self.player_to_room[player_id]
        room = self.rooms[code]
        room.remove_player(player_id)
        del self.player_to_room[player_id]
        
        # If room is empty, delete it
        if len(room.players) == 0:
            del self.rooms[code]
        else:
            # Reset room state if only one player left
            room.state = GameState.WAITING
            room.reset_choices()
        
        return code
    
    def make_choice(self, player_id: str, choice: str) -> tuple[bool, Optional[dict]]:
        """Player makes a choice (rock, paper, scissors)"""
        if player_id not in self.player_to_room:
            return False, None
        
        code = self.player_to_room[player_id]
        room = self.rooms[code]
        player = room.get_player(player_id)
        
        if not player or room.state != GameState.PLAYING:
            return False, None
        
        try:
            player.choice = Choice(choice.lower())
            player.last_activity = time.time()
            room.last_activity = time.time()
        except ValueError:
            return False, None
        
        # Check if both players have made their choice
        if room.both_players_chose():
            result = self._determine_winner(room)
            room.state = GameState.FINISHED
            return True, result
        
        return True, None
    
    def _determine_winner(self, room: GameRoom) -> dict:
        """Determine the winner of the game"""
        p1, p2 = room.players[0], room.players[1]
        
        if p1.choice == p2.choice:
            result = "tie"
            winner_id = None
        elif (
            (p1.choice == Choice.ROCK and p2.choice == Choice.SCISSORS) or
            (p1.choice == Choice.PAPER and p2.choice == Choice.ROCK) or
            (p1.choice == Choice.SCISSORS and p2.choice == Choice.PAPER)
        ):
            result = "win"
            winner_id = p1.id
            room.winner = p1.id
        else:
            result = "win"
            winner_id = p2.id
            room.winner = p2.id
        
        return {
            "result": result,
            "winner_id": winner_id,
            "choices": {
                p1.id: p1.choice.value,
                p2.id: p2.choice.value
            },
            "players": {
                p1.id: p1.name,
                p2.id: p2.name
            }
        }
    
    def get_room(self, code: str) -> Optional[GameRoom]:
        """Get room by code"""
        return self.rooms.get(code)
    
    def get_player_room(self, player_id: str) -> Optional[GameRoom]:
        """Get room that player is in"""
        if player_id not in self.player_to_room:
            return None
        code = self.player_to_room[player_id]
        return self.rooms.get(code)
    
    def cleanup_expired_rooms(self, max_age_minutes: int = 30):
        """Remove rooms that have been inactive for too long"""
        current_time = time.time()
        expired_codes = []
        
        for code, room in self.rooms.items():
            if current_time - room.last_activity > max_age_minutes * 60:
                expired_codes.append(code)
        
        for code in expired_codes:
            room = self.rooms[code]
            # Remove player mappings
            for player in room.players:
                if player.id in self.player_to_room:
                    del self.player_to_room[player.id]
            # Remove room
            del self.rooms[code]
        
        return len(expired_codes)
