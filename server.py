from flask import Flask, request
from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room
import threading
import time
import os
from game_manager import GameManager

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-change-in-production')
socketio = SocketIO(app, cors_allowed_origins="*", logger=True, engineio_logger=True)

# Global game manager
game_manager = GameManager()

def cleanup_task():
    """Background task to cleanup expired rooms"""
    while True:
        time.sleep(300)  # Run every 5 minutes
        cleaned = game_manager.cleanup_expired_rooms(30)  # Remove rooms older than 30 minutes
        if cleaned > 0:
            print(f"Cleaned up {cleaned} expired rooms")

# Start cleanup task in background
cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
cleanup_thread.start()

@app.route('/')
def index():
    return {"message": "Rock Paper Scissors Backend API", "status": "running"}

@app.route('/health')
def health():
    return {"status": "healthy"}

@socketio.on('connect')
def on_connect():
    print(f"Client connected: {request.sid}")
    emit('connected', {'player_id': request.sid})

@socketio.on('disconnect')
def on_disconnect():
    print(f"Client disconnected: {request.sid}")
    room_code = game_manager.leave_room(request.sid)
    if room_code:
        # Notify other players in the room
        room = game_manager.get_room(room_code)
        if room:
            socketio.emit('player_left', {
                'message': 'Your opponent has left the game'
            }, room=room_code)

@socketio.on('create_room')
def on_create_room(data):
    player_name = data.get('player_name', 'Anonymous')
    room_code = game_manager.create_room(request.sid, player_name)
    
    join_room(room_code)
    
    emit('room_created', {
        'room_code': room_code,
        'player_id': request.sid,
        'player_name': player_name
    })
    
    print(f"Room {room_code} created by {player_name}")

@socketio.on('join_room')
def on_join_room(data):
    room_code = data.get('room_code', '').upper()
    player_name = data.get('player_name', 'Anonymous')
    
    success, message = game_manager.join_room(room_code, request.sid, player_name)
    
    if success:
        join_room(room_code)
        room = game_manager.get_room(room_code)
        
        # Notify the joiner
        emit('room_joined', {
            'room_code': room_code,
            'player_id': request.sid,
            'player_name': player_name,
            'players': [{'id': p.id, 'name': p.name} for p in room.players]
        })
        
        # Notify all players in the room about the new player
        socketio.emit('player_joined', {
            'player_id': request.sid,
            'player_name': player_name,
            'players': [{'id': p.id, 'name': p.name} for p in room.players],
            'game_ready': room.is_full()
        }, room=room_code)
        
        print(f"Player {player_name} joined room {room_code}")
    else:
        emit('join_error', {'message': message})

@socketio.on('make_choice')
def on_make_choice(data):
    choice = data.get('choice')
    
    success, result = game_manager.make_choice(request.sid, choice)
    
    if success:
        room = game_manager.get_player_room(request.sid)
        if room:
            # Notify the player that their choice was recorded
            emit('choice_recorded', {'choice': choice})
            
            # If both players have chosen, send results to all players in room
            if result:
                socketio.emit('game_result', result, room=room.code)
                print(f"Game finished in room {room.code}: {result}")
            else:
                # Notify other players that this player has made a choice
                socketio.emit('player_chose', {
                    'player_id': request.sid,
                    'waiting_for_other': True
                }, room=room.code)
    else:
        emit('choice_error', {'message': 'Invalid choice or game state'})

@socketio.on('play_again')
def on_play_again(data):
    room = game_manager.get_player_room(request.sid)
    if room and room.state.value == 'finished':
        room.reset_choices()

        # Notify all players in the room
        socketio.emit('new_round', {
            'message': 'Starting new round!',
            'current_round': room.current_round,
            'scores': room.get_scores()
        }, room=room.code)

        print(f"New round started in room {room.code}")

@socketio.on('get_room_status')
def on_get_room_status():
    room = game_manager.get_player_room(request.sid)
    if room:
        emit('room_status', {
            'room_code': room.code,
            'state': room.state.value,
            'players': [{'id': p.id, 'name': p.name, 'connected': p.connected} for p in room.players],
            'is_full': room.is_full()
        })

if __name__ == '__main__':
    print("Starting Rock Paper Scissors server...")
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('DEBUG', 'False').lower() == 'true'

    if debug:
        print(f"Server will be available at http://localhost:{port}")
    else:
        print(f"Server starting on port {port}")

    socketio.run(app, host='0.0.0.0', port=port, debug=debug)
