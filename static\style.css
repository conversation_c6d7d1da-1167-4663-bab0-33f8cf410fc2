/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 2rem;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Screen Management */
.screen {
    display: none;
    flex: 1;
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    animation: slideIn 0.3s ease-out;
}

.screen.active {
    display: flex;
    flex-direction: column;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Welcome Screen */
.welcome-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    gap: 2rem;
}

/* Input Groups */
.input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
    max-width: 300px;
}

.input-group label {
    font-weight: 600;
    color: #555;
}

input[type="text"] {
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

input[type="text"]:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Buttons */
.button-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
    max-width: 300px;
}

.btn {
    padding: 14px 24px;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover:before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.btn-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(245, 87, 108, 0.4);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 87, 108, 0.6);
}

.btn-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9rem;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Room Code Display */
.room-info {
    text-align: center;
    margin-bottom: 2rem;
}

.room-code-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    color: white;
}

.room-code {
    font-size: 2rem;
    font-weight: bold;
    letter-spacing: 0.2em;
    padding: 0.5rem 1rem;
    background: rgba(255,255,255,0.2);
    border-radius: 10px;
    font-family: 'Courier New', monospace;
}

/* Players List */
.players-list h3 {
    text-align: center;
    margin-bottom: 1rem;
    color: #555;
}

.players-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.player-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 10px;
    font-weight: 500;
}

.player-status {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Game Screen */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.room-code-small {
    font-weight: 600;
    color: #667eea;
}

.game-status {
    text-align: center;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 2rem;
    padding: 1rem;
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-radius: 10px;
    color: #333;
}

/* Choice Buttons */
.choices-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.choice-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 2rem 1rem;
    border: 3px solid transparent;
    border-radius: 15px;
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    font-weight: 600;
}

.choice-btn:hover {
    transform: translateY(-5px);
    border-color: #667eea;
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.choice-btn.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.choice-emoji {
    font-size: 3rem;
}

.choice-name {
    font-size: 1.1rem;
}

/* Results Screen */
.results-content {
    text-align: center;
}

.result-header h2 {
    margin-bottom: 2rem;
    font-size: 2rem;
    color: #333;
}

.choices-display {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 2rem 0;
    flex-wrap: wrap;
    gap: 1rem;
}

.player-choice {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.player-name {
    font-weight: 600;
    color: #555;
}

.choice-display {
    font-size: 4rem;
    padding: 1rem;
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 50%;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.vs-divider {
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
}

.result-message {
    font-size: 1.3rem;
    font-weight: 600;
    margin: 2rem 0;
    padding: 1rem;
    border-radius: 10px;
}

.result-message.win {
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    color: #2d5a27;
}

.result-message.lose {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    color: #d63031;
}

.result-message.tie {
    background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
    color: #2d3436;
}

/* Connection Status */
.connection-status {
    position: fixed;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255,255,255,0.9);
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1001;
}

.toast {
    background: #333;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    animation: toastSlide 0.3s ease-out;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}

@keyframes toastSlide {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Error Messages */
.error-message {
    color: #e74c3c;
    font-weight: 500;
    text-align: center;
    margin-top: 1rem;
    padding: 0.5rem;
    background: rgba(231, 76, 60, 0.1);
    border-radius: 5px;
    display: none;
}

.error-message.show {
    display: block;
}

/* Status Messages */
.status-message {
    text-align: center;
    font-size: 1.1rem;
    color: #666;
    margin: 2rem 0;
    padding: 1rem;
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    border-radius: 10px;
}

.choice-feedback {
    text-align: center;
    font-size: 1.1rem;
    font-weight: 600;
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 10px;
    display: none;
}

.choice-feedback.show {
    display: block;
}

.choice-feedback.waiting {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    color: #d63031;
}

.choice-feedback.recorded {
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    color: #2d5a27;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .screen {
        padding: 1.5rem;
    }
    
    .choices-container {
        grid-template-columns: 1fr;
    }
    
    .choice-btn {
        padding: 1.5rem 1rem;
    }
    
    .choice-emoji {
        font-size: 2.5rem;
    }
    
    .choices-display {
        flex-direction: column;
        gap: 2rem;
    }
    
    .vs-divider {
        order: 2;
    }
    
    .game-header {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .room-code {
        font-size: 1.5rem;
    }

    .choice-display {
        width: 80px;
        height: 80px;
        font-size: 3rem;
    }

    .button-group {
        gap: 0.5rem;
    }
}

/* Additional Utility Classes */
.hidden {
    display: none !important;
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}
